import { Request } from 'express';
import { User } from '@prisma/client';

export interface AuthenticatedRequest extends Request {
  user?: User;
}

export interface JWTPayload {
  userId: string;
  email: string;
}

export interface CreatePostData {
  title: string;
  content: string;
  excerpt?: string;
  published?: boolean;
  featuredImage?: string;
}

export interface UpdatePostData {
  title?: string;
  content?: string;
  excerpt?: string;
  published?: boolean;
  featuredImage?: string;
}

export interface CreateCommentData {
  content: string;
}

export interface RegisterData {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

{"name": "blog-application", "version": "1.0.0", "description": "A modern blog application with React, Node.js, and PostgreSQL", "private": true, "workspaces": ["client", "server"], "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "install:all": "npm install && cd client && npm install && cd ../server && npm install", "build": "cd client && npm run build && cd ../server && npm run build"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}}
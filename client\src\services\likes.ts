import api from './api';
import { LikesResponse } from '@/types';

export const likesService = {
  async getPostLikes(postId: string): Promise<LikesResponse> {
    const response = await api.get(`/likes/post/${postId}`);
    return response.data;
  },

  async toggleLike(postId: string): Promise<{ message: string; liked: boolean }> {
    const response = await api.post(`/likes/post/${postId}`);
    return response.data;
  },
};

import { Router } from 'express';
import authRoutes from './auth';
import postRoutes from './posts';
import commentRoutes from './comments';
import likeRoutes from './likes';

const router = Router();

router.use('/auth', authRoutes);
router.use('/posts', postRoutes);
router.use('/comments', commentRoutes);
router.use('/likes', likeRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

export default router;

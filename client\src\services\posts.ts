import api from './api';
import { Post, PostsResponse, CreatePostData, UpdatePostData } from '@/types';

export const postsService = {
  async getAllPosts(page = 1, limit = 10): Promise<PostsResponse> {
    const response = await api.get(`/posts?page=${page}&limit=${limit}`);
    return response.data;
  },

  async getPostById(id: string): Promise<{ post: Post }> {
    const response = await api.get(`/posts/${id}`);
    return response.data;
  },

  async getPostBySlug(slug: string): Promise<{ post: Post }> {
    const response = await api.get(`/posts/slug/${slug}`);
    return response.data;
  },

  async createPost(data: CreatePostData): Promise<{ message: string; post: Post }> {
    const response = await api.post('/posts', data);
    return response.data;
  },

  async updatePost(id: string, data: UpdatePostData): Promise<{ message: string; post: Post }> {
    const response = await api.put(`/posts/${id}`, data);
    return response.data;
  },

  async deletePost(id: string): Promise<{ message: string }> {
    const response = await api.delete(`/posts/${id}`);
    return response.data;
  },

  async getUserPosts(page = 1, limit = 10): Promise<PostsResponse> {
    const response = await api.get(`/posts/user/me?page=${page}&limit=${limit}`);
    return response.data;
  },
};

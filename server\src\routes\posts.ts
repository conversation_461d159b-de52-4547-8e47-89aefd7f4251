import { Router } from 'express';
import {
  getAllPosts,
  getPostById,
  getPostBySlug,
  createPost,
  updatePost,
  deletePost,
  getUserPosts,
} from '../controllers/postController';
import { authenticate } from '../middleware/auth';
import { validate, createPostSchema, updatePostSchema } from '../middleware/validation';

const router = Router();

// Public routes
router.get('/', getAllPosts);
router.get('/slug/:slug', getPostBySlug);
router.get('/:id', getPostById);

// Protected routes
router.post('/', authenticate, validate(createPostSchema), createPost);
router.put('/:id', authenticate, validate(updatePostSchema), updatePost);
router.delete('/:id', authenticate, deletePost);
router.get('/user/me', authenticate, getUserPosts);

export default router;

{"name": "blog-server", "version": "1.0.0", "description": "Backend server for blog application", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cookie-parser": "^1.4.6", "zod": "^3.22.4", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cookie-parser": "^1.4.6", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^5.7.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}
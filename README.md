# Modern Blog Application

A full-stack blog application built with React, Node.js, TypeScript, and PostgreSQL.

## 🚀 Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development
- **TailwindCSS** for styling
- **React Router DOM** for routing
- **Axios** for API communication

### Backend
- **Node.js** with Express and TypeScript
- **Prisma ORM** with PostgreSQL
- **JWT** authentication with HTTP-only cookies
- **bcrypt** for password hashing
- **Helmet** and **CORS** for security

## ✨ Features

- 🔐 **User Authentication** - Register, login, logout with JWT
- 📝 **Blog Posts** - Create, read, update, delete posts
- 💬 **Comments** - Comment on posts
- ❤️ **Likes** - Like/unlike posts
- 🛡️ **Protected Routes** - Dashboard and post management
- 📱 **Responsive Design** - Mobile-friendly interface

## 🛠️ Local Development Setup

### Prerequisites
- Node.js 18+ 
- PostgreSQL 12+
- npm or yarn

### 1. Clone and Install Dependencies

```bash
# Install root dependencies
npm install

# Install all workspace dependencies
npm run install:all
```

### 2. Database Setup

1. **Install PostgreSQL** locally or use a cloud service
2. **Create a database** named `blog_app`
3. **Copy environment files**:

```bash
# Server environment
cp server/.env.example server/.env

# Client environment  
cp client/.env.example client/.env
```

4. **Configure server/.env**:
```env
DATABASE_URL="postgresql://username:password@localhost:5432/blog_app"
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN="7d"
PORT=4000
NODE_ENV="development"
```

5. **Configure client/.env**:
```env
VITE_API_URL=http://localhost:4000
```

### 3. Database Migration

```bash
cd server
npx prisma migrate dev --name init
npx prisma generate
```

### 4. Start Development Servers

```bash
# Start both frontend and backend
npm run dev

# Or start individually:
npm run dev:server  # Backend on http://localhost:4000
npm run dev:client  # Frontend on http://localhost:5173
```

## 📁 Project Structure

```
blog-application/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   ├── hooks/         # Custom hooks
│   │   ├── context/       # React context
│   │   └── types/         # TypeScript types
│   ├── public/
│   └── package.json
├── server/                # Node.js backend
│   ├── src/
│   │   ├── controllers/   # Route controllers
│   │   ├── routes/        # API routes
│   │   ├── middleware/    # Custom middleware
│   │   ├── services/      # Business logic
│   │   ├── utils/         # Utility functions
│   │   └── config/        # Configuration
│   ├── prisma/           # Database schema
│   └── package.json
└── package.json          # Root workspace config
```

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user

### Posts
- `GET /api/posts` - Get all posts
- `GET /api/posts/:id` - Get single post
- `POST /api/posts` - Create post (auth required)
- `PUT /api/posts/:id` - Update post (auth required)
- `DELETE /api/posts/:id` - Delete post (auth required)

### Comments
- `GET /api/posts/:id/comments` - Get post comments
- `POST /api/posts/:id/comments` - Add comment (auth required)
- `DELETE /api/comments/:id` - Delete comment (auth required)

### Likes
- `POST /api/posts/:id/like` - Toggle like (auth required)
- `GET /api/posts/:id/likes` - Get post likes

## 🧪 Testing

```bash
# Run backend tests
cd server && npm test

# Run frontend tests
cd client && npm test
```

## 🐳 Production Deployment

### Docker (Optional)
```bash
# Build and run with Docker Compose
docker-compose up --build
```

### Environment Variables for Production
Update your `.env` files with production values:
- Database connection string
- JWT secret (use a strong, random key)
- CORS origins
- API URLs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

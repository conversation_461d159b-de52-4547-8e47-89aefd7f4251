import { Router } from 'express';
import { toggleLike, getPostLikes } from '../controllers/likeController';
import { authenticate, optionalAuth } from '../middleware/auth';

const router = Router();

// Get likes for a post (public, but with optional auth for user context)
router.get('/post/:postId', optionalAuth, getPostLikes);

// Toggle like (protected)
router.post('/post/:postId', authenticate, toggleLike);

export default router;

import api from './api';
import { Comment, CommentsResponse } from '@/types';

export const commentsService = {
  async getPostComments(postId: string, page = 1, limit = 20): Promise<CommentsResponse> {
    const response = await api.get(`/comments/post/${postId}?page=${page}&limit=${limit}`);
    return response.data;
  },

  async createComment(postId: string, content: string): Promise<{ message: string; comment: Comment }> {
    const response = await api.post(`/comments/post/${postId}`, { content });
    return response.data;
  },

  async deleteComment(id: string): Promise<{ message: string }> {
    const response = await api.delete(`/comments/${id}`);
    return response.data;
  },
};

import { Router } from 'express';
import { getPostComments, createComment, deleteComment } from '../controllers/commentController';
import { authenticate, optionalAuth } from '../middleware/auth';
import { validate, createCommentSchema } from '../middleware/validation';

const router = Router();

// Get comments for a post (public, but with optional auth for user context)
router.get('/post/:postId', optionalAuth, getPostComments);

// Create comment (protected)
router.post('/post/:postId', authenticate, validate(createCommentSchema), createComment);

// Delete comment (protected)
router.delete('/:id', authenticate, deleteComment);

export default router;

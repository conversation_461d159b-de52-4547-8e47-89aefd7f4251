import { Response } from 'express';
import { AuthenticatedRequest } from '../types';
import prisma from '../config/database';

export const toggleLike = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const { postId } = req.params;

    // Check if post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    if (!post.published) {
      return res.status(400).json({ error: 'Cannot like unpublished post' });
    }

    // Check if user already liked the post
    const existingLike = await prisma.like.findUnique({
      where: {
        userId_postId: {
          userId: req.user.id,
          postId,
        },
      },
    });

    if (existingLike) {
      // Unlike the post
      await prisma.like.delete({
        where: {
          userId_postId: {
            userId: req.user.id,
            postId,
          },
        },
      });

      res.json({
        message: 'Post unliked successfully',
        liked: false,
      });
    } else {
      // Like the post
      await prisma.like.create({
        data: {
          userId: req.user.id,
          postId,
        },
      });

      res.json({
        message: 'Post liked successfully',
        liked: true,
      });
    }
  } catch (error) {
    console.error('Toggle like error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getPostLikes = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { postId } = req.params;

    // Check if post exists
    const post = await prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    const likesCount = await prisma.like.count({
      where: { postId },
    });

    let isLiked = false;
    if (req.user) {
      const userLike = await prisma.like.findUnique({
        where: {
          userId_postId: {
            userId: req.user.id,
            postId,
          },
        },
      });
      isLiked = !!userLike;
    }

    res.json({
      likesCount,
      isLiked,
    });
  } catch (error) {
    console.error('Get post likes error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};
